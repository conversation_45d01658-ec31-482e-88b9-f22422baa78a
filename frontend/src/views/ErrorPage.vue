<template>
  <phantomlayout>
    <div class="max-w-md w-full mx-auto p-8">
      <div
        class="bg-gray-800/70 backdrop-blur-md rounded-xl border border-red-500/30 shadow-glow-md p-8 text-center"
      >
        <!-- Error Icon -->
        <div class="mb-6">
          <svg
            class="h-16 w-16 text-red-500 mx-auto"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
            />
          </svg>
        </div>

        <!-- Error Title -->
        <h1
          class="text-2xl font-bold bg-gradient-to-r from-red-400 to-red-600 bg-clip-text text-transparent mb-4"
        >
          Authentication Error
        </h1>

        <!-- Error Message -->
        <p class="text-gray-300 mb-6">
          {{ errorMessage }}
        </p>

        <!-- Error Details (if available) -->
        <div v-if="errorDetails" class="mb-6">
          <details class="text-left">
            <summary class="text-gray-400 cursor-pointer hover:text-gray-300">
              Technical Details
            </summary>
            <pre
              class="mt-2 p-3 bg-gray-900/50 rounded text-xs text-gray-400 overflow-auto"
              >{{ errorDetails }}</pre
            >
          </details>
        </div>

        <!-- Action Buttons -->
        <div class="space-y-3">
          <button
            class="w-full bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-600 hover:to-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-105"
            @click="tryAgain"
          >
            Try Again
          </button>

          <button
            class="w-full bg-gray-700 hover:bg-gray-600 text-gray-300 font-semibold py-3 px-6 rounded-lg transition-all duration-200"
            @click="goToLogin"
          >
            Go to Login
          </button>
        </div>

        <!-- Help Text -->
        <p class="text-gray-500 text-sm mt-6">
          If this problem persists, please contact your system administrator.
        </p>
      </div>
    </div>
  </phantomlayout>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import { error, info } from "@/utils/logger";

const router = useRouter();
const route = useRoute();

const errorMessage = ref("An error occurred during authentication.");
const errorDetails = ref(null);

onMounted(() => {
  // Get error details from query parameters or localStorage
  const urlParams = new URLSearchParams(window.location.search);
  const errorParam = urlParams.get("error");
  const errorDescription = urlParams.get("error_description");

  // Try to get more details from localStorage (set by callback)
  const storedError = localStorage.getItem("auth_error");
  if (storedError) {
    try {
      const errorData = JSON.parse(storedError);
      errorDetails.value = JSON.stringify(errorData, null, 2);
      localStorage.removeItem("auth_error"); // Clean up
    } catch (e) {
      error("Failed to parse stored error data:", e);
    }
  }

  // Set error message based on available information
  if (errorParam) {
    switch (errorParam) {
      case "access_denied":
        errorMessage.value =
          "Access was denied. You may have cancelled the login process.";
        break;
      case "invalid_request":
        errorMessage.value =
          "Invalid authentication request. Please try logging in again.";
        break;
      case "server_error":
        errorMessage.value =
          "Authentication server error. Please try again later.";
        break;
      case "temporarily_unavailable":
        errorMessage.value =
          "Authentication service is temporarily unavailable.";
        break;
      case "authentication_failed":
        errorMessage.value =
          "Authentication failed. Please check your credentials and try again.";
        break;
      case "no_user_info":
        errorMessage.value =
          "Unable to retrieve user information. Please try logging in again.";
        break;
      default:
        errorMessage.value = `Authentication error: ${errorParam}`;
    }

    if (errorDescription) {
      errorMessage.value += ` (${errorDescription})`;
    }
  } else if (route.query.message) {
    errorMessage.value = route.query.message;
  }

  info("Error page loaded", {
    errorParam,
    errorDescription,
    hasStoredError: !!storedError,
  });
});

const tryAgain = () => {
  info("User clicked try again on error page");
  // Clear any stored error state
  localStorage.removeItem("auth_error");
  localStorage.removeItem("used_auth_code");
  localStorage.removeItem("oauth_state");

  // Redirect to login to start fresh
  router.push("/login");
};

const goToLogin = () => {
  info("User clicked go to login on error page");
  // Clear any stored error state
  localStorage.removeItem("auth_error");
  localStorage.removeItem("used_auth_code");
  localStorage.removeItem("oauth_state");

  router.push("/login");
};
</script>

<style scoped>
.shadow-glow-md {
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06),
    0 0 0 1px rgba(59, 130, 246, 0.1);
}
</style>
